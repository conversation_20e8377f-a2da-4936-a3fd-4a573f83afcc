import 'package:flutter/material.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/Utilities/app_text_style.dart';
import 'package:sepesha_app/Utilities/app_images.dart';
import 'package:sepesha_app/l10n/app_localizations.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: const Text('About'),
        backgroundColor: AppColor.white,
        foregroundColor: AppColor.blackText,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations.appName,
                    style: AppTextStyle.heading2(AppColor.primary).copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '${localizations.version} 1.0.0',
                    style: AppTextStyle.subtext1(AppColor.grey),
                  ),
                ],
              ),
              ],
            ),
            
            const SizedBox(height: 40),
            
            _buildListTile(
              icon: Icons.star_outline,
              title: 'Rate the app',
              onTap: () => _launchURL('https://sepesha.com/rate-app', context),
            ),
            _buildListTile(
              icon: Icons.thumb_up_outlined,
              title: 'Like us on Facebook',
              onTap: () => _launchURL('https://sepesha.com/facebook', context),
            ),
            _buildListTile(
              icon: Icons.business_center_outlined,
              title: 'Solutions for work rides',
              onTap: () => _launchURL('https://sepesha.com/work-rides', context),
            ),
            _buildListTile(
              icon: Icons.work_outline,
              title: 'Careers at Sepesha',
              onTap: () => _launchURL('https://sepesha.com/careers', context),
            ),
            _buildListTile(
              icon: Icons.accessibility,
              title: 'Accessibility',
              onTap: () => _launchURL('https://sepesha.com/accessibility', context),
            ),
            _buildListTile(
              icon: Icons.gavel_outlined,
              title: 'Legal',
              onTap: () => _launchURL('https://sepesha.com/legal', context),
            ),
            _buildListTile(
              icon: Icons.shield_outlined,
              title: 'Privacy',
              onTap: () => _launchURL('https://sepesha.com/privacy', context),
            ),
            
            const SizedBox(height: 40),
            
            Center(
              child: Text(
                localizations.copyright,
                textAlign: TextAlign.center,
                style: AppTextStyle.subtext1(AppColor.grey),
              ),
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildListTile({
    required IconData icon,
    required String title, 
    required VoidCallback onTap
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: TextButton(
        onPressed: onTap,
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          alignment: Alignment.centerLeft,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: AppColor.grey,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: AppTextStyle.paragraph1(AppColor.blackText),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColor.grey,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchURL(String url, BuildContext context) async {
  final Uri uri = Uri.parse(url);
  if (await canLaunchUrl(uri)) {
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  } else {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Could not open link $url'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
  }
}