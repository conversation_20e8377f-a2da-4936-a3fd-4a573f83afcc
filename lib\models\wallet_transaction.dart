class WalletTransaction {
  final String id;
  final String type; // 'credit', 'debit', 'withdrawal'
  final double amount;
  final String currency;
  final String description;
  final DateTime timestamp;
  final String? rideId;
  final TransactionStatus status;

  WalletTransaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.currency,
    required this.description,
    required this.timestamp,
    this.rideId,
    required this.status,
  });

  factory WalletTransaction.fromJson(Map<String, dynamic> json) {
    return WalletTransaction(
      id: json['id'] ?? '',
      type: json['type'] ?? 'credit',
      amount: double.tryParse(json['amount']?.toString() ?? '0') ?? 0.0,
      currency: json['currency'] ?? 'TZS',
      description: json['description'] ?? '',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      rideId: json['ride_id'],
      status: TransactionStatusExtension.fromString(json['status'] ?? 'completed'),
    );
  }
}

enum TransactionStatus { pending, completed, failed }

extension TransactionStatusExtension on TransactionStatus {
  static TransactionStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return TransactionStatus.pending;
      case 'failed':
        return TransactionStatus.failed;
      default:
        return TransactionStatus.completed;
    }
  }
}