import 'package:flutter/material.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/Utilities/app_text_style.dart';
import 'package:sepesha_app/models/wallet_transaction.dart';
import 'package:intl/intl.dart';

class WalletTransactionCard extends StatelessWidget {
  final WalletTransaction transaction;

  const WalletTransactionCard({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    final isCredit = transaction.type == 'credit';
    final color = isCredit ? AppColor.successColor : AppColor.primary;
    final icon = isCredit ? Icons.arrow_downward : Icons.arrow_upward;
    final prefix = isCredit ? '+' : '-';

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        title: Text(
          transaction.description,
          style: AppTextStyle.paragraph1(AppColor.blackText),
        ),
        subtitle: Text(
          DateFormat('MMM dd, yyyy • HH:mm').format(transaction.timestamp),
          style: AppTextStyle.subtext1(AppColor.grey),
        ),
        trailing: Text(
          '$prefix${transaction.currency} ${transaction.amount.toStringAsFixed(2)}',
          style: AppTextStyle.paragraph1(color).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}