class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String vehicleNumber;
  final String vehicleType;
  final double walletBalance;
  final double rating;
  final int totalRides;
  final bool isVerified;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.vehicleNumber,
    required this.vehicleType,
    required this.walletBalance,
    required this.rating,
    required this.totalRides,
    required this.isVerified,
  });
}